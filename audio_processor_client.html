<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>木偶AI翻唱 - 音频处理客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px dashed #ddd;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .mode-selection {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
            justify-content: center;
        }

        .mode-option {
            flex: 1;
            max-width: 300px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .mode-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .mode-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .mode-option h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .mode-option p {
            color: #666;
            font-size: 0.9em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .control-group label {
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
        }

        .process-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto 25px;
        }

        .process-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .process-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: #333;
            font-weight: bold;
        }

        .results-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            display: none;
        }

        .audio-result {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .audio-result h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .audio-result audio {
            width: 100%;
            margin-bottom: 10px;
        }

        .download-btn {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
        }

        .status-message {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            display: none;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .mode-selection {
                flex-direction: column;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 木偶AI翻唱</h1>
            <p>专业的音频分离与音色转换工具</p>
        </div>

        <div class="status-message" id="statusMessage"></div>

        <div class="upload-section">
            <h3>📁 选择音频文件</h3>
            <p>支持 WAV, MP3, FLAC, OGG, M4A, AAC 格式</p>
            <input type="file" id="audioFile" class="file-input" accept="audio/*">
            <button class="upload-btn" onclick="document.getElementById('audioFile').click()">
                选择文件
            </button>
            <div id="fileName" style="margin-top: 10px; color: #666;"></div>
        </div>

        <div class="mode-selection">
            <div class="mode-option selected" data-mode="complete">
                <h3>🎵 完整模式</h3>
                <p>分离音频 → 降噪处理 → 音色转换<br>
                输出：伴奏、人声、转换后人声</p>
            </div>
            <div class="mode-option" data-mode="vocal_only">
                <h3>🎙️ 干声模式</h3>
                <p>降噪处理 → 音色转换<br>
                输出：转换后人声</p>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>音调偏移</label>
                <input type="number" id="keyShift" value="0" min="-12" max="12">
            </div>
            <div class="control-group">
                <label>共振峰偏移</label>
                <input type="number" id="formantShift" value="0" min="-12" max="12">
            </div>
            <div class="control-group">
                <label>F0提取器</label>
                <select id="f0Extractor">
                    <option value="rmvpe">RMVPE</option>
                    <option value="crepe">CREPE</option>
                    <option value="harvest">Harvest</option>
                </select>
            </div>
            <div class="control-group">
                <label>推理步数</label>
                <input type="number" id="inferStep" value="10" min="1" max="50">
            </div>
            <div class="control-group">
                <label>启用降噪</label>
                <input type="checkbox" id="enableDenoise" checked>
            </div>
        </div>

        <button class="process-btn" id="processBtn" onclick="startProcessing()">
            🚀 开始处理
        </button>

        <div class="progress-section" id="progressSection">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备中...</div>
        </div>

        <div class="results-section" id="resultsSection">
            <h3>🎉 处理结果</h3>
            <div id="audioResults"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8001';
        let selectedMode = 'complete';
        let currentTaskId = null;
        let progressInterval = null;

        // 文件选择处理
        document.getElementById('audioFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = `已选择: ${file.name}`;
            }
        });

        // 模式选择处理
        document.querySelectorAll('.mode-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.mode-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedMode = this.dataset.mode;
            });
        });

        // 显示状态消息
        function showStatus(message, isError = false) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message ${isError ? 'status-error' : 'status-success'}`;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        // 开始处理
        async function startProcessing() {
            const fileInput = document.getElementById('audioFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showStatus('请先选择音频文件', true);
                return;
            }

            try {
                // 禁用按钮
                document.getElementById('processBtn').disabled = true;
                document.getElementById('progressSection').style.display = 'block';
                document.getElementById('resultsSection').style.display = 'none';
                
                updateProgress(0, '正在上传文件...');

                // 上传文件
                const formData = new FormData();
                formData.append('file', file);
                
                const uploadResponse = await fetch(`${API_BASE_URL}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!uploadResponse.ok) {
                    throw new Error('文件上传失败');
                }
                
                const uploadData = await uploadResponse.json();
                const fileId = uploadData.file_id;
                
                updateProgress(10, '文件上传成功，开始处理...');

                // 开始处理
                const processData = {
                    mode: selectedMode,
                    model_name: 'YSML',
                    key_shift: parseInt(document.getElementById('keyShift').value),
                    formant_shift: parseInt(document.getElementById('formantShift').value),
                    vocal_register_shift: 0,
                    f0_extractor: document.getElementById('f0Extractor').value,
                    infer_step: parseInt(document.getElementById('inferStep').value),
                    method: 'euler',
                    spk_id: 0,
                    enable_denoise: document.getElementById('enableDenoise').checked
                };

                const processResponse = await fetch(`${API_BASE_URL}/process?file_id=${fileId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(processData)
                });

                if (!processResponse.ok) {
                    throw new Error('处理请求失败');
                }

                const processResult = await processResponse.json();
                currentTaskId = processResult.task_id;
                
                updateProgress(15, '处理任务已创建，正在监控进度...');
                
                // 开始监控进度
                startProgressMonitoring();

            } catch (error) {
                showStatus(`处理失败: ${error.message}`, true);
                document.getElementById('processBtn').disabled = false;
                document.getElementById('progressSection').style.display = 'none';
            }
        }

        // 更新进度
        function updateProgress(percent, message) {
            document.getElementById('progressFill').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = message;
        }

        // 开始进度监控
        function startProgressMonitoring() {
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE_URL}/tasks/${currentTaskId}`);
                    if (!response.ok) {
                        throw new Error('获取任务状态失败');
                    }

                    const taskData = await response.json();
                    const progress = Math.round(taskData.progress * 100);
                    
                    updateProgress(progress, taskData.message);

                    if (taskData.status === 'completed') {
                        clearInterval(progressInterval);
                        showResults(taskData.result);
                        showStatus('处理完成！');
                        document.getElementById('processBtn').disabled = false;
                    } else if (taskData.status === 'error') {
                        clearInterval(progressInterval);
                        showStatus(`处理失败: ${taskData.error}`, true);
                        document.getElementById('processBtn').disabled = false;
                        document.getElementById('progressSection').style.display = 'none';
                    }
                } catch (error) {
                    clearInterval(progressInterval);
                    showStatus(`监控进度失败: ${error.message}`, true);
                    document.getElementById('processBtn').disabled = false;
                }
            }, 2000);
        }

        // 显示结果
        function showResults(results) {
            const resultsContainer = document.getElementById('audioResults');
            resultsContainer.innerHTML = '';

            const audioTypes = {
                'accompaniment': '🎵 伴奏',
                'harmony': '🎶 和声',
                'separated_vocals': '🎙️ 分离人声',
                'denoised_vocals': '🔇 降噪人声',
                'converted_vocals': '✨ 转换后人声'
            };

            for (const [key, filePath] of Object.entries(results)) {
                const audioDiv = document.createElement('div');
                audioDiv.className = 'audio-result';

                const title = audioTypes[key] || key;
                const filename = filePath.split('/').pop(); // 获取文件名

                audioDiv.innerHTML = `
                    <h4>${title}</h4>
                    <audio controls>
                        <source src="${API_BASE_URL}/download/${filename}" type="audio/wav">
                        您的浏览器不支持音频播放
                    </audio>
                    <br>
                    <a href="${API_BASE_URL}/download/${filename}" class="download-btn" download="${filename}">
                        📥 下载
                    </a>
                `;

                resultsContainer.appendChild(audioDiv);
            }

            document.getElementById('resultsSection').style.display = 'block';
        }
    </script>
</body>
</html>
